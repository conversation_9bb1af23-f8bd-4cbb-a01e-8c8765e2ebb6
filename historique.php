<?php
/**
 * Page Historique des Commandes
 * Affichage des commandes avec filtres et détails
 */

require_once 'pos_config.php';

// Vérifier la connexion
if (!$pos->isConnected()) {
    die('Erreur de connexion à la base de données');
}

// Récupérer les paramètres de filtre
$dateDebut = $_GET['date_debut'] ?? date('Y-m-d', strtotime('-7 days'));
$dateFin = $_GET['date_fin'] ?? date('Y-m-d');
$serveur = $_GET['serveur'] ?? '';
$modePaiement = $_GET['mode_paiement'] ?? '';
$commandeSelectionnee = $_GET['commande_id'] ?? '';

// Récupérer la liste de TOUS les serveurs pour le filtre
try {
    $stmtServeurs = $pos->pdo->prepare("SELECT IDServeur, Nom FROM serveur ORDER BY Nom");
    $stmtServeurs->execute();
    $serveurs = $stmtServeurs->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $serveurs = [];
}

// Récupérer la liste de TOUS les modes de paiement pour le filtre
try {
    $stmtModes = $pos->pdo->prepare("SELECT TypeMode, PyeMode FROM ModePye ORDER BY PyeMode");
    $stmtModes->execute();
    $modesPaiement = $stmtModes->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $modesPaiement = [];
}

// Construire la requête des tickets avec filtres (équivalent WinDev)
// POUR TOUT tickets AVEC IDServeur = combo_serveur ET mode_paiement = combo_modpye ET periode entre date_debut et date_fin
$whereConditions = [];
$params = [];

// Convertir les dates au format numérique YYYYMMDD (format de la base)
$dateDebutNum = str_replace('-', '', $dateDebut); // 2025-01-15 -> 20250115
$dateFinNum = str_replace('-', '', $dateFin);

// Période entre date début et date fin (format numérique HSQL)
$whereConditions[] = "t.DATE >= ? AND t.DATE <= ?";
$params[] = $dateDebutNum;
$params[] = $dateFinNum;

// IDServeur = combo_serveur (ValeurMémorisée)
if (!empty($serveur)) {
    $whereConditions[] = "t.IDServeur = ?";
    $params[] = $serveur;
}

// mode de paiement = combo_modpye
if (!empty($modePaiement)) {
    $whereConditions[] = "t.typepaye = ?";
    $params[] = $modePaiement;
}

$whereClause = implode(' AND ', $whereConditions);

// Récupérer les tickets (équivalent TableAjouteLigne WinDev)
try {
    $sqlCommandes = "SELECT
        t.IDtickets,
        t.DATE as date_cmd,
        t.heure as heure_cmd,
        t.IDTables,
        t.NumTick as numero_ticket,
        t.total,
        s.Nom as serveur_nom,
        t.typepaye as mode_paiement,
        t.IDServeur
    FROM tickets t
    LEFT JOIN serveur s ON t.IDServeur = s.IDServeur
    WHERE {$whereClause}
    ORDER BY t.DATE DESC, t.heure DESC";
    
    $stmtCommandes = $pos->pdo->prepare($sqlCommandes);
    $stmtCommandes->execute($params);
    $commandes = $stmtCommandes->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $commandes = [];
    $errorMessage = "Erreur lors de la récupération des commandes : " . $e->getMessage();
}

// Récupérer les détails du ticket sélectionné (équivalent WinDev VteJour)
$detailsCommande = [];
if (!empty($commandeSelectionnee)) {
    try {
        $sqlDetails = "SELECT
            v.articles as article,
            v.quantite,
            v.total as sous_total,
            v.IDVteJour,
            v.IDarticle,
            v.NumCategories
        FROM VteJour v
        WHERE v.IDtickets = ?
        ORDER BY v.articles";

        $stmtDetails = $pos->pdo->prepare($sqlDetails);
        $stmtDetails->execute([$commandeSelectionnee]);
        $detailsCommande = $stmtDetails->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $detailsCommande = [];
    }
}

// Calculer les statistiques de la période (tickets)
$totalTickets = count($commandes);
$totalChiffre = array_sum(array_column($commandes, 'total'));
$moyenneTicket = $totalTickets > 0 ? $totalChiffre / $totalTickets : 0;
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historique des Commandes - POS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .filters-section {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #555;
        }

        .filter-group input,
        .filter-group select {
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .content-grid {
            display: block;
        }

        .commandes-section {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
        }

        .table-container {
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .commande-row {
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .commande-row.selected {
            background: #e3f2fd;
        }

        .details-section {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }

        .no-selection {
            padding: 40px;
            text-align: center;
            color: #666;
        }

        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .total-highlight {
            font-weight: bold;
            color: #28a745;
        }

        .details-section {
            border-top: 3px solid #667eea;
        }

        .commande-row:hover {
            background: #f8f9fa;
            cursor: pointer;
        }

        .commande-row.selected {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
        }



        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- En-tête -->
        <div class="header">
            <h1>📊 Historique</h1>
        </div>

        <!-- Section Filtres -->
        <div class="filters-section">
            <form method="GET" action="">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="date_debut">📅 Date de début</label>
                        <input type="date" id="date_debut" name="date_debut" value="<?php echo htmlspecialchars($dateDebut); ?>">
                    </div>
                    
                    <div class="filter-group">
                        <label for="date_fin">📅 Date de fin</label>
                        <input type="date" id="date_fin" name="date_fin" value="<?php echo htmlspecialchars($dateFin); ?>">
                    </div>
                    
                    <div class="filter-group">
                        <label for="serveur">👤 Serveur</label>
                        <select id="serveur" name="serveur">
                            <option value="">Tous les serveurs</option>
                            <?php foreach ($serveurs as $srv): ?>
                                <option value="<?php echo htmlspecialchars($srv['IDServeur']); ?>" <?php echo $serveur == $srv['IDServeur'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($srv['Nom']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="mode_paiement">💳 Mode de paiement</label>
                        <select id="mode_paiement" name="mode_paiement">
                            <option value="">Tous les modes</option>
                            <?php foreach ($modesPaiement as $mode): ?>
                                <option value="<?php echo htmlspecialchars($mode['TypeMode']); ?>" <?php echo $modePaiement === $mode['TypeMode'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($mode['PyeMode']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <button type="submit" class="btn">🔍 Filtrer</button>
            </form>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value"><?php echo $totalTickets; ?></div>
                <div class="stat-label">Tickets</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($totalChiffre, 2); ?>€</div>
                <div class="stat-label">Chiffre d'affaires</div>
            </div>
        </div>

        <!-- Liste des tickets -->
        <div class="commandes-section">
            <div class="section-header">
                <h2 class="section-title">🎫 Tickets (<?php echo $totalTickets; ?>)</h2>
            </div>
                
                <div class="table-container">
                    <?php if (!empty($commandes)): ?>
                        <table>
                            <thead>
                                <tr>
                                    <th>Ticket</th>
                                    <th>Total</th>
                                    <th>Serveur</th>
                                    <th>Date</th>
                                    <th>Heure</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($commandes as $ticket): ?>
                                    <tr class="commande-row <?php echo $commandeSelectionnee == $ticket['IDtickets'] ? 'selected' : ''; ?>"
                                        onclick="window.location.href='?<?php echo http_build_query(array_merge($_GET, ['commande_id' => $ticket['IDtickets']])); ?>'"
                                        title="Cliquer pour voir les détails du ticket">
                                        <td>#<?php echo htmlspecialchars($ticket['numero_ticket']); ?></td>
                                        <td class="total-highlight"><?php echo number_format($ticket['total'], 2); ?>€</td>
                                        <td><?php echo htmlspecialchars($ticket['serveur_nom'] ?? 'N/A'); ?></td>
                                        <td><?php
                                            // Convertir format numérique YYYYMMDD en date lisible
                                            $dateNum = $ticket['date_cmd'];
                                            $dateFormatted = substr($dateNum, 6, 2) . '/' . substr($dateNum, 4, 2) . '/' . substr($dateNum, 0, 4);
                                            echo $dateFormatted;
                                        ?></td>
                                        <td><?php echo substr($ticket['heure_cmd'], 0, 8); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div class="no-selection">
                            <p>Aucune commande trouvée pour cette période</p>
                        </div>
                    <?php endif; ?>
                </div>
        </div>

        <!-- Détails du ticket sélectionné -->
        <div class="details-section" style="margin-top: 30px;">
                <div class="section-header">
                    <h2 class="section-title">🛍️ Détails du ticket
                        
                    </h2>
                </div>

                <?php if (!empty($commandeSelectionnee)): ?>
                    <?php if (!empty($detailsCommande)): ?>
                        <div class="table-container">
                            <table class="details-table">
                                <thead>
                                    <tr>
                                        <th>Article</th>
                                        <th>Qté</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $totalDetails = 0;
                                    foreach ($detailsCommande as $detail):
                                        $totalDetails += $detail['sous_total'];
                                    ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($detail['article']); ?></td>
                                            <td><?php echo $detail['quantite']; ?></td>
                                            <td class="total-highlight"><?php echo number_format($detail['sous_total'], 2); ?>€</td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr style="background: #f8f9fa; font-weight: bold;">
                                        <td colspan="2">Total</td>
                                        <td class="total-highlight"><?php echo number_format($totalDetails, 2); ?>€</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="no-selection">
                            <p>Aucun détail trouvé pour ce ticket</p>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="no-selection">
                        <p>Cliquez sur une ligne de ticket pour voir ses détails</p>
                    </div>
                <?php endif; ?>

            </div>
        </div>
    </div>


</body>
</html>

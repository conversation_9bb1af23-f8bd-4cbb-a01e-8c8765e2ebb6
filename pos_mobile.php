<?php
require_once 'pos_config.php';

// Gestion des actions POST (formulaires)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'add_to_cart':
            $articleId = intval($_POST['article_id']);
            $quantity = intval($_POST['quantity'] ?? 1);
            $pos->addToCart($articleId, $quantity);
            break;

        case 'update_cart':
            $articleId = intval($_POST['article_id']);
            $quantity = intval($_POST['quantity']);
            $pos->updateCartQuantity($articleId, $quantity);
            break;

        case 'remove_from_cart':
            $articleId = intval($_POST['article_id']);
            $pos->removeFromCart($articleId);
            break;

        case 'process_order':
            $paymentMethod = $_POST['payment_method'] ?? 'cash';
            $orderId = $pos->processOrder($paymentMethod);
            break;

        case 'clear_cart':
            $pos->clearCart();
            break;
    }

    // Redirection pour éviter la resoumission du formulaire
    // Préserver les paramètres GET (catégorie, recherche) après les actions POST
    $redirectParams = [];

    if (isset($_POST['category']) && !empty($_POST['category'])) {
        $redirectParams['category'] = $_POST['category'];
    }

    if (isset($_POST['search']) && !empty($_POST['search'])) {
        $redirectParams['search'] = $_POST['search'];
    }

    $redirectUrl = $_SERVER['PHP_SELF'];
    if (!empty($redirectParams)) {
        $redirectUrl .= '?' . http_build_query($redirectParams);
    }

    header('Location: ' . $redirectUrl);
    exit;
}

// Récupération des données pour l'affichage
$categories = $pos->getCategories();
$selectedCategory = $_GET['category'] ?? '';
$searchQuery = $_GET['search'] ?? '';

if ($searchQuery) {
    $articles = $pos->searchArticles($searchQuery);
} else {
    $articles = $pos->getArticlesByCategory($selectedCategory ?: null);
}

$cart = $pos->getCart();
$cartTotal = $pos->getCartTotalTTC();
$todayStats = $pos->getTodayStats();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?php echo POS_NAME; ?> - Point de Vente</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="POS">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .mobile-menu-icon {
            display: none;
            background: none;
            border: none;
            font-size: 24px;
            color: #2c3e50;
            cursor: pointer;
            padding: 5px;
        }

        .header-menu {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .menu-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            border: none;
            background: transparent;
            color: #666;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 11px;
            min-width: 60px;
        }

        .menu-btn:hover {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }

        .menu-btn.active {
            background: #3498db;
            color: white;
        }

        .menu-btn i {
            font-size: 16px;
        }

        .menu-btn span {
            font-weight: 500;
        }

        /* Search Bar */
        .search-container {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            outline: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .search-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            font-size: 18px;
            cursor: pointer;
        }

        /* Categories Sidebar - Left */
        .categories-sidebar {
            position: fixed;
            left: 0;
            top: 50px;
            bottom: 0;
            width: 200px;
            background: #2c3e50;
            padding: 20px 0;
            z-index: 500; /* Z-index plus bas que la cart */
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            transform: translateX(-100%); /* Fermée par défaut sur mobile */
            transition: transform 0.3s ease;
        }

        .categories-sidebar.mobile-open {
            transform: translateX(0);
        }

        .categories-list {
            display: flex;
            flex-direction: column;
            gap: 5px;
            padding: 0 10px;
        }

        .category-btn {
            width: 100%;
            padding: 15px 12px;
            border: none;
            background: transparent;
            color: #bdc3c7;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-radius: 8px;
            margin-bottom: 2px;
        }

        .category-btn.active {
            color: #ffffff;
            background: #3498db;
        }

        .category-btn:hover {
            color: #ffffff;
            background: rgba(52, 152, 219, 0.7);
        }



        .category-name {
            font-weight: 600;
        }

        .category-icon {
            font-size: 16px;
            min-width: 15px;
            color: #6b7280;
        }



        /* Main Content */
        .main-content {
            display: flex;
            gap: 20px;
            padding: 0 20px 20px 220px; /* Left padding for sidebar */
            min-height: calc(100vh - 100px);
        }

        /* Articles Grid */
        .articles-section {
            flex: 2;
            padding-top: 5px;
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 12px;
        }

        .article-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            height: 200px;
        }

        .article-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .article-image {
            width: 100%;
            height: 120px;
            background: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }

        .article-img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            object-position: center;
            display: block;
            padding: 8px;
            box-sizing: border-box;
        }

        .no-image-placeholder {
            width: 100%;
            height: 100%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 14px;
            text-align: center;
        }

        .article-price-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .article-info {
            padding: 12px;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .article-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            line-height: 1.2;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .article-category {
            font-size: 11px;
            color: #7f8c8d;
            margin-bottom: 8px;
        }

        .article-stock {
            font-size: 11px;
            color: #27ae60;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .article-stock.insufficient {
            color: #e74c3c;
        }





        .add-btn {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: #3498db;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .add-btn:hover {
            background: #2980b9;
            transform: scale(1.1);
        }







        /* Nouvelle Cart Section - Structure en 3 parties */
        .cart-section {
            flex: 1;
            min-width: 350px;
            max-width: 400px;
            background: white;
            border-radius: 15px;
            height: fit-content;
            position: sticky;
            top: 100px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 1. HEADER CART */
        .cart-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
            flex-shrink: 0;
        }

        .cart-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .cart-header-text {
            text-align: center;
            flex: 1;
        }

        .cart-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .cart-subtitle {
            font-size: 14px;
            color: #666;
        }

        .cart-close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #666;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
        }

        .cart-close-btn:hover {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            transform: scale(1.1);
        }

        /* 2. BODY CART - Liste avec scroll */
        .cart-body {
            flex: 1;
            overflow-y: auto;
            max-height: 400px;
            padding: 0;
        }

        .cart-items {
            padding: 20px;
        }

        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .cart-item-info {
            flex: 1;
        }

        .cart-item-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .cart-item-price {
            color: #666;
            font-size: 14px;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .qty-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            background: #f0f0f0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .qty-btn:hover {
            background: #3498db;
            color: white;
        }

        .quantity {
            font-weight: 600;
            min-width: 30px;
            text-align: center;
        }

        .empty-cart {
            text-align: center;
            padding: 40px 20px;
            color: #95a5a6;
        }

        .empty-cart-icon {
            font-size: 60px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* 3. FOOTER CART - Total + Boutons */
        .cart-footer {
            background: white;
            border-top: 2px solid #e9ecef;
            flex-shrink: 0;
        }

        .cart-total {
            background: #f8f9fa;
            padding: 15px 20px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
        }

        .total-amount {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
        }

        .cart-actions {
            display: flex;
            gap: 15px;
            padding: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-validate {
            background: #28a745;
            color: white;
        }

        .btn-validate:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .btn-cancel {
            background: #dc3545;
            color: white;
        }

        .btn-cancel:hover {
            background: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }



        /* Empty States */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: rgba(255, 255, 255, 0.8);
        }

        .empty-icon {
            font-size: 60px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        /* Empty cart state */
        .empty-cart {
            text-align: center;
            padding: 40px 20px;
            color: #95a5a6;
            background: white;
        }

        .empty-cart-icon {
            font-size: 60px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
                padding: 0 20px 120px 20px; /* Padding égal sur mobile */
            }

            /* Cart Section Mobile - Nouvelle structure */
            .cart-section {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                transform: translateY(calc(100% - 70px)); /* Fermée par défaut */
                transition: transform 0.3s ease;
                z-index: 2000;
                height: calc(100vh - 40px); /* Plus d'espace en bas */
                max-height: calc(100vh - 40px);
                border-radius: 20px 20px 0 0;
                box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
                display: flex;
                flex-direction: column;
                overflow: hidden; /* Retour à hidden mais avec plus d'espace */
            }

            .cart-section.expanded {
                transform: translateY(0);
            }

            /* Header mobile */
            .cart-header {
                flex-shrink: 0;
                background: #f8f9fa;
                border-bottom: 2px solid #e9ecef;
                position: relative;
                padding: 15px 20px !important; /* Padding réduit sur mobile */
            }

            .cart-header::after {
                content: '';
                position: absolute;
                top: 8px;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 4px;
                background: #ddd;
                border-radius: 2px;
            }

            .cart-header-content {
                margin-top: 10px; /* Espace pour la poignée */
            }

            .cart-close-btn {
                font-size: 20px !important; /* Taille réduite sur mobile */
                width: 35px !important;
                height: 35px !important;
                padding: 6px !important;
            }

            /* Body mobile avec scroll */
            .cart-body {
                flex: 1;
                overflow-y: auto;
                max-height: calc(100vh - 250px); /* Plus d'espace pour header + footer */
                min-height: 150px;
            }

            .cart-items {
                padding: 20px;
            }

            /* Footer mobile - Position fixe pour éviter le masquage */
            .cart-footer {
                position: fixed !important;
                bottom: 50px !important; /* 10px du bas pour être visible */
                left: 10px !important;
                right: 10px !important;
                background: white !important;
                border-top: 2px solid #e9ecef !important;
                border-radius: 0 0 15px 15px !important;
                box-shadow: 0 -2px 10px rgba(0,0,0,0.1) !important;
                z-index: 99999 !important;
                display: block !important;
                max-height: 150px !important; /* Hauteur maximale */
            }

            /* Boutons sur mobile */
            .cart-actions {
                display: flex !important;
                gap: 15px !important;
                padding: 15px 20px !important; /* Moins de padding vertical */
                background: white !important;
                position: relative !important;
                z-index: 99999 !important;
                min-height: 60px !important; /* Hauteur réduite */
            }

            .action-btn {
                flex: 1 !important;
                padding: 12px 15px !important; /* Padding réduit */
                border: none !important;
                border-radius: 10px !important;
                font-weight: 700 !important;
                cursor: pointer !important;
                font-size: 16px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                gap: 8px !important;
                min-height: 45px !important; /* Hauteur minimale */
                max-height: 50px !important; /* Hauteur maximale */
            }

            /* Ajuster le body pour laisser de l'espace au footer fixe */
            .cart-body {
                padding-bottom: 140px !important; /* Espace pour le footer fixe */
            }

            /* Total plus compact sur mobile */
            .cart-total {
                padding: 10px 20px !important; /* Padding réduit */
                margin: 0 !important;
                background: #f8f9fa !important;
                border-bottom: 1px solid #e9ecef !important;
            }

            .total-amount {
                font-size: 20px !important; /* Taille réduite */
                font-weight: 700 !important;
                color: #2c3e50 !important;
                margin: 0 !important;
            }

            .action-btn {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .cart-toggle-btn {
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                width: 65px;
                height: 65px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 26px;
                z-index: 999;
                box-shadow:
                    0 8px 25px rgba(102, 126, 234, 0.4),
                    0 15px 35px rgba(102, 126, 234, 0.2),
                    0 0 0 0 rgba(102, 126, 234, 0.3);
                transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                animation: pulse-shadow 2s infinite;
            }

            @keyframes pulse-shadow {
                0% {
                    box-shadow:
                        0 8px 25px rgba(102, 126, 234, 0.4),
                        0 15px 35px rgba(102, 126, 234, 0.2),
                        0 0 0 0 rgba(102, 126, 234, 0.3);
                }
                50% {
                    box-shadow:
                        0 12px 35px rgba(102, 126, 234, 0.5),
                        0 20px 45px rgba(102, 126, 234, 0.3),
                        0 0 0 10px rgba(102, 126, 234, 0.1);
                }
                100% {
                    box-shadow:
                        0 8px 25px rgba(102, 126, 234, 0.4),
                        0 15px 35px rgba(102, 126, 234, 0.2),
                        0 0 0 0 rgba(102, 126, 234, 0.3);
                }
            }

            .cart-toggle-btn:hover {
                transform: translateX(-50%) scale(1.15);
                box-shadow:
                    0 15px 40px rgba(102, 126, 234, 0.6),
                    0 25px 60px rgba(102, 126, 234, 0.4),
                    0 0 0 15px rgba(102, 126, 234, 0.1);
                animation: none;
            }

            .cart-toggle-btn:active {
                transform: translateX(-50%) scale(1.05);
                transition: all 0.1s ease;
            }

            .cart-toggle-btn .cart-count {
                position: absolute;
                top: -8px;
                right: -8px;
                background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                color: white;
                border-radius: 50%;
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 13px;
                font-weight: 700;
                border: 3px solid white;
                box-shadow:
                    0 4px 12px rgba(231, 76, 60, 0.4),
                    0 2px 6px rgba(231, 76, 60, 0.2);
                animation: bounce-in 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            }

            @keyframes bounce-in {
                0% {
                    transform: scale(0);
                    opacity: 0;
                }
                50% {
                    transform: scale(1.2);
                    opacity: 0.8;
                }
                100% {
                    transform: scale(1);
                    opacity: 1;
                }
            }











            .articles-grid {
                grid-template-columns: 1fr 1fr; /* Exactement 2 colonnes */
                gap: 10px;
            }

            .header-menu {
                gap: 5px;
            }

            .menu-btn {
                min-width: 50px;
                padding: 6px 8px;
                font-size: 10px;
            }

            .menu-btn i {
                font-size: 14px;
            }

            .menu-btn span {
                display: none;
            }

            .search-container {
                padding: 15px;
            }

            .header {
                padding: 10px 15px;
            }

            .logo {
                display: none;
            }

            .mobile-menu-icon {
                display: block;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Success Animation */
        .success-animation {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(40, 167, 69, 0.95);
            color: white;
            padding: 20px 40px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 1000;
            animation: successPop 0.5s ease-out;
        }

        @keyframes successPop {
            0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        }

        /* Warning Animation */
        .warning-animation {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 193, 7, 0.95);
            color: #212529;
            padding: 20px 40px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 1000;
            animation: warningPop 0.5s ease-out;
            border: 2px solid #ffc107;
        }

        @keyframes warningPop {
            0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        }



        @media (min-width: 769px) {
            .mobile-menu-icon {
                display: none;
            }

            .cart-toggle-btn {
                display: none;
            }

            /* Desktop - Nouvelle structure */
            .cart-section {
                position: sticky;
                top: 100px;
                transform: none;
                height: fit-content;
                border-radius: 15px;
                box-shadow: 0 2px 15px rgba(0,0,0,0.1);
                max-height: none;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            .cart-header {
                background: #f8f9fa;
                border-bottom: 2px solid #e9ecef;
                padding: 20px !important; /* Padding normal sur desktop */
            }

            .cart-header::after {
                display: none; /* Pas de handle sur desktop */
            }

            .cart-header-content {
                margin-top: 0 !important; /* Pas de marge sur desktop */
            }

            .cart-close-btn {
                font-size: 24px !important; /* Taille normale sur desktop */
                width: 40px !important;
                height: 40px !important;
                padding: 8px !important;
            }

            .cart-body {
                max-height: 400px;
                overflow-y: auto;
            }

            .cart-items {
                padding: 20px;
            }

            .cart-footer {
                background: white;
                border-top: 2px solid #e9ecef;
                box-shadow: none;
            }

            /* Sidebar toujours visible sur desktop */
            .categories-sidebar {
                transform: translateX(0) !important;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <button class="mobile-menu-icon" onclick="toggleCategories()">
                <i class="fas fa-bars"></i>
            </button>
            <div class="logo">
                <i class="fas fa-coffee"></i>
            </div>
            <div class="header-menu">
                <button class="menu-btn active" onclick="selectMenu('commandes')">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Commandes</span>
                </button>
                <button class="menu-btn" onclick="selectMenu('historique')">
                    <i class="fas fa-history"></i>
                    <span>Historique</span>
                </button>
                <button class="menu-btn" onclick="selectMenu('analyses')">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analyses</span>
                </button>
                <button class="menu-btn" onclick="selectMenu('parametres')">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </button>
            </div>
        </div>
    </div>





    <!-- Main Content -->
    <div class="main-content">
        <!-- Articles Section -->
        <div class="articles-section">
            <?php if (empty($articles)): ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>Aucun article trouvé</h3>
                    <p>Essayez de modifier votre recherche ou sélectionnez une autre catégorie</p>
                </div>
            <?php else: ?>
                <div class="articles-grid">
                    <?php foreach ($articles as $article): ?>
                        <?php
                        $cardClass = 'article-card';
                        $stockClass = 'article-stock';
                        ?>
                        <div class="<?php echo $cardClass; ?>" onclick="addToCart(<?php echo $article['IDarticles']; ?>)">
                            <div class="article-image">
                                <?php
                                // SYSTÈME FICHIERS EXPORTÉS: Simple et fiable
                                $articleId = $article['IDarticles'];
                                $imagePaths = [
                                    "images/articles/{$articleId}.png",
                                    "images/articles/{$articleId}.jpg",
                                    "images/articles/{$articleId}.jpeg",
                                    "images/articles/{$articleId}.gif"
                                ];

                                $imageUrl = null;
                                foreach ($imagePaths as $path) {
                                    if (file_exists($path)) {
                                        $imageUrl = $path;
                                        break;
                                    }
                                }
                                ?>

                                <?php if ($imageUrl): ?>
                                    <!-- Article <?php echo $articleId; ?> - Image: <?php echo basename($imageUrl); ?> -->
                                    <img src="<?php echo $imageUrl; ?>"
                                         alt="<?php echo htmlspecialchars($article['designation']); ?>"
                                         class="article-img"
                                         onload="console.log('✅ Image fichier chargée pour article <?php echo $articleId; ?>:', this.src);"
                                         onerror="console.log('❌ Erreur image fichier pour article <?php echo $articleId; ?>:', this.src); this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="no-image-placeholder" style="display: none;">
                                        Image non disponible
                                    </div>
                                <?php else: ?>
                                    <div class="no-image-placeholder">
                                        Pas d'image
                                    </div>
                                <?php endif; ?>
                                <div class="article-price-badge"><?php echo formatPrice($pos->getArticlePrice($article['IDarticles'])); ?></div>
                            </div>
                            <div class="article-info">
                                <div>
                                    <div class="article-name"><?php echo htmlspecialchars($article['designation']); ?></div>
                                    <div class="article-category"><?php echo htmlspecialchars($article['nom_categorie']); ?></div>
                                </div>
                                <div class="<?php echo $stockClass; ?>">
                                    <i class="fas fa-box"></i>
                                    <?php echo $article['quantite']; ?>
                                </div>
                            </div>
                            <button class="add-btn" onclick="event.stopPropagation(); addToCart(<?php echo $article['IDarticles']; ?>)">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Bouton Panier Flottant -->
        <button class="cart-toggle-btn" onclick="toggleCart()" id="cartToggleBtn">
            <i class="fas fa-shopping-cart"></i>
            <span class="cart-count" id="cartCount" style="display: <?php echo !empty($cart) ? 'flex' : 'none'; ?>">
                <?php echo count($cart); ?>
            </span>
        </button>

        <!-- Nouvelle Cart Section - Structure en 3 parties -->
        <div class="cart-section" id="cartSection">
            <!-- 1. HEADER CART -->
            <div class="cart-header">
                <div class="cart-header-content">
                    <div class="cart-header-text">
                        <div class="cart-title">Commande Actuelle</div>
                        <div class="cart-subtitle">Table 103</div>
                    </div>
                    <button class="cart-close-btn" onclick="closeCart()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- 2. BODY CART - Liste avec scroll -->
            <div class="cart-body">
                <div class="cart-items" id="cartItems">
                    <?php if (empty($cart)): ?>
                        <div class="empty-cart">
                            <div class="empty-cart-icon">🛒</div>
                            <p>Sélectionnez des articles pour commencer</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($cart as $articleId => $item): ?>
                            <div class="cart-item" data-article-id="<?php echo $articleId; ?>">
                                <div class="cart-item-info">
                                    <div class="cart-item-name"><?php echo htmlspecialchars($item['article']['designation']); ?></div>
                                    <div class="cart-item-price"><?php echo formatPrice($item['price']); ?> × <?php echo $item['quantity']; ?></div>
                                </div>
                                <div class="quantity-controls">
                                    <button class="qty-btn" onclick="updateQuantity(<?php echo $articleId; ?>, <?php echo $item['quantity'] - 1; ?>)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <span class="quantity"><?php echo $item['quantity']; ?></span>
                                    <button class="qty-btn" onclick="updateQuantity(<?php echo $articleId; ?>, <?php echo $item['quantity'] + 1; ?>)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 3. FOOTER CART - Total + Boutons -->
            <div class="cart-footer">
                <?php if (!empty($cart)): ?>
                    <!-- Total -->
                    <div class="cart-total">
                        <div class="total-amount" id="cartTotal"><?php echo formatPrice($cartTotal); ?></div>
                    </div>

                    <!-- Boutons -->
                    <div class="cart-actions">
                        <button class="action-btn btn-validate" onclick="processPayment('confirm')">
                            <i class="fas fa-check"></i> Valider
                        </button>
                        <button class="action-btn btn-cancel" onclick="clearCart()">
                            <i class="fas fa-times"></i> Annuler
                        </button>
                    </div>
                <?php else: ?>
                    <!-- Bouton pour panier vide -->
                    <div class="cart-actions">
                        <button class="action-btn btn-cancel" onclick="closeCart()" style="width: 100%;">
                            <i class="fas fa-times"></i> Fermer
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>

    </div>






    <!-- Categories Sidebar -->
    <div class="categories-sidebar" id="categoriesSidebar">
        <div class="categories-list">
            <button class="category-btn <?php echo empty($selectedCategory) ? 'active' : ''; ?>"
                    onclick="selectCategory('')">
                <div class="category-icon">-</div>
                <div class="category-name">Tous les articles</div>
            </button>

            <?php foreach ($categories as $category): ?>
                <button class="category-btn <?php echo $selectedCategory == $category['IDCategorie'] ? 'active' : ''; ?>"
                        onclick="selectCategory(<?php echo $category['IDCategorie']; ?>)">
                    <div class="category-icon">-</div>
                    <div class="category-name"><?php echo htmlspecialchars($category['categories']); ?></div>
                </button>
            <?php endforeach; ?>

            <button class="category-btn" onclick="selectCategory('combo')">
                <div class="category-icon">-</div>
                <div class="category-name">Combo</div>
            </button>

            <button class="category-btn" onclick="selectCategory('other')">
                <div class="category-icon">-</div>
                <div class="category-name">Autres</div>
            </button>
        </div>
    </div>

    <script>
        // Variables globales
        let isLoading = false;

        // Fonctions de navigation
        function selectCategory(categoryId) {
            window.location.href = `?category=${categoryId}`;
        }

        function selectMenu(menuItem) {
            // Retirer la classe active de tous les boutons
            document.querySelectorAll('.menu-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Ajouter la classe active au bouton cliqué
            event.target.closest('.menu-btn').classList.add('active');

            // Gérer la navigation selon le menu sélectionné
            switch(menuItem) {
                case 'commandes':
                    // Rester sur la page actuelle (POS)
                    break;
                case 'historique':
                    // Rediriger vers la page historique
                    window.location.href = 'historique.php';
                    break;
                case 'analyses':
                    // Rediriger vers la page analyses
                    window.location.href = 'analyses.php';
                    break;
                case 'parametres':
                    // Rediriger vers la page paramètres
                    window.location.href = 'parametres.php';
                    break;
            }
        }

        function performSearch() {
            const searchValue = document.getElementById('searchInput').value;
            window.location.href = `?search=${encodeURIComponent(searchValue)}`;
        }

        // Gestion du panier
        function addToCart(articleId, quantity = 1) {
            if (isLoading) return;
            isLoading = true;

            // Fermer la sidebar des catégories si ouverte (mobile)
            if (window.innerWidth <= 768) {
                const categoriesSidebar = document.getElementById('categoriesSidebar');
                if (categoriesSidebar && categoriesSidebar.classList.contains('mobile-open')) {
                    categoriesSidebar.classList.remove('mobile-open');
                }
            }

            // Récupérer les paramètres actuels de l'URL pour les préserver
            const urlParams = new URLSearchParams(window.location.search);
            const currentCategory = urlParams.get('category') || '';
            const currentSearch = urlParams.get('search') || '';

            // Créer un formulaire caché pour soumettre les données
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'add_to_cart';

            const articleInput = document.createElement('input');
            articleInput.type = 'hidden';
            articleInput.name = 'article_id';
            articleInput.value = articleId;

            const quantityInput = document.createElement('input');
            quantityInput.type = 'hidden';
            quantityInput.name = 'quantity';
            quantityInput.value = quantity;

            // Ajouter les paramètres de navigation pour les préserver
            if (currentCategory) {
                const categoryInput = document.createElement('input');
                categoryInput.type = 'hidden';
                categoryInput.name = 'category';
                categoryInput.value = currentCategory;
                form.appendChild(categoryInput);
            }

            if (currentSearch) {
                const searchInput = document.createElement('input');
                searchInput.type = 'hidden';
                searchInput.name = 'search';
                searchInput.value = currentSearch;
                form.appendChild(searchInput);
            }

            form.appendChild(actionInput);
            form.appendChild(articleInput);
            form.appendChild(quantityInput);
            document.body.appendChild(form);

            // Soumettre le formulaire
            form.submit();
        }

        function updateQuantity(articleId, newQuantity) {
            if (isLoading) return;
            isLoading = true;

            // Récupérer les paramètres actuels de l'URL pour les préserver
            const urlParams = new URLSearchParams(window.location.search);
            const currentCategory = urlParams.get('category') || '';
            const currentSearch = urlParams.get('search') || '';

            // Créer un formulaire caché pour soumettre les données
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'update_cart';

            const articleInput = document.createElement('input');
            articleInput.type = 'hidden';
            articleInput.name = 'article_id';
            articleInput.value = articleId;

            const quantityInput = document.createElement('input');
            quantityInput.type = 'hidden';
            quantityInput.name = 'quantity';
            quantityInput.value = newQuantity;

            // Ajouter les paramètres de navigation pour les préserver
            if (currentCategory) {
                const categoryInput = document.createElement('input');
                categoryInput.type = 'hidden';
                categoryInput.name = 'category';
                categoryInput.value = currentCategory;
                form.appendChild(categoryInput);
            }

            if (currentSearch) {
                const searchInput = document.createElement('input');
                searchInput.type = 'hidden';
                searchInput.name = 'search';
                searchInput.value = currentSearch;
                form.appendChild(searchInput);
            }

            form.appendChild(actionInput);
            form.appendChild(articleInput);
            form.appendChild(quantityInput);
            document.body.appendChild(form);

            // Soumettre le formulaire
            form.submit();
        }

        function processPayment(method) {
            if (isLoading) return;

            if (!confirm('Confirmer la commande ?')) return;

            isLoading = true;

            // Créer un formulaire caché pour soumettre les données
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'process_order';

            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = 'payment_method';
            methodInput.value = method;

            form.appendChild(actionInput);
            form.appendChild(methodInput);
            document.body.appendChild(form);

            // Soumettre le formulaire
            form.submit();
        }

        function clearCart() {
            if (!confirm('Vider le panier ?')) return;

            // Créer un formulaire caché pour soumettre les données
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'clear_cart';

            form.appendChild(actionInput);
            document.body.appendChild(form);

            // Soumettre le formulaire
            form.submit();
        }

        function updateCartDisplay() {
            // Recharger la page pour mettre à jour l'affichage du panier
            window.location.reload();
        }

        // Fonctions AJAX supprimées - utilisation de formulaires maintenant

        // Fonctions utilitaires
        function showSuccess(message) {
            const div = document.createElement('div');
            div.className = 'success-animation';
            div.innerHTML = `<i class="fas fa-check"></i> ${message}`;
            document.body.appendChild(div);

            setTimeout(() => {
                div.remove();
            }, 3000);
        }

        function showError(message) {
            alert(message); // Remplacer par une notification plus élégante
        }

        function showWarning(message) {
            const div = document.createElement('div');
            div.className = 'warning-animation';
            div.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
            document.body.appendChild(div);

            setTimeout(() => {
                div.remove();
            }, 4000);
        }

        // Gestion mobile
        function toggleCart() {
            const cartSection = document.getElementById('cartSection');
            cartSection.classList.toggle('expanded');
        }

        // Fonction pour fermer la cart
        function closeCart() {
            const cartSection = document.getElementById('cartSection');
            cartSection.classList.remove('expanded');
        }

        // Fonction pour ouvrir la cart
        function openCart() {
            const cartSection = document.getElementById('cartSection');
            cartSection.classList.add('expanded');
        }

        // Fermer le panel des catégories en cliquant en dehors (mobile uniquement)
        document.addEventListener('click', function(event) {
            if (window.innerWidth <= 768) {
                const categoriesSidebar = document.getElementById('categoriesSidebar');
                const mobileMenuIcon = document.querySelector('.mobile-menu-icon');

                // Si le panel est ouvert et qu'on clique en dehors
                if (categoriesSidebar && categoriesSidebar.classList.contains('mobile-open')) {
                    if (!categoriesSidebar.contains(event.target) &&
                        !mobileMenuIcon.contains(event.target)) {
                        categoriesSidebar.classList.remove('mobile-open');
                    }
                }
            }
        });

        function toggleCategories() {
            const sidebar = document.getElementById('categoriesSidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // Fermer la sidebar quand on clique en dehors (mobile)
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('categoriesSidebar');
            const toggle = document.querySelector('.mobile-category-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggle.contains(event.target)) {
                sidebar.classList.remove('mobile-open');
            }
        });

        // Gestion responsive
        function handleResize() {
            const cartToggleBtn = document.getElementById('cartToggleBtn');
            const cartSection = document.getElementById('cartSection');

            if (window.innerWidth <= 768) {
                cartToggleBtn.style.display = 'flex';
                // S'assurer que la cart est fermée sur mobile par défaut
                cartSection.classList.remove('expanded');
            } else {
                cartToggleBtn.style.display = 'none';
                // S'assurer que la cart est visible sur desktop
                cartSection.classList.remove('expanded');
            }
        }

        // Test de diagnostic
        function testCartElements() {
            console.log('=== TEST DIAGNOSTIC ===');
            console.log('cartItems existe:', !!document.getElementById('cartItems'));
            console.log('cartTotal existe:', !!document.getElementById('cartTotal'));
            console.log('mobileCartTotal existe:', !!document.getElementById('mobileCartTotal'));
            console.log('cartCount existe:', !!document.getElementById('cartCount'));

            const cartItems = document.getElementById('cartItems');
            if (cartItems) {
                console.log('cartItems contenu actuel:', cartItems.innerHTML.substring(0, 100));
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            handleResize();

            // Test diagnostic au chargement
            setTimeout(testCartElements, 1000);

            // Recherche en temps réel
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 2 || this.value.length === 0) {
                        performSearch();
                    }
                }, 500);
            });

            // Gestion du clavier
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });

        window.addEventListener('resize', handleResize);

        // PWA - Service Worker (optionnel)
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('sw.js').catch(console.error);
        }
    </script>
</body>
</html>
